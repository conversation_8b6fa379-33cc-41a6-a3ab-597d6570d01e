import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  View, Text, TouchableOpacity, StatusBar, ActivityIndicator, Alert, StyleSheet
} from 'react-native';
import { VideoView, useVideoPlayer } from 'expo-video';
import * as ScreenOrientation from 'expo-screen-orientation';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect } from '@react-navigation/native';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, runOnJS } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import BottomSheet, { BottomSheetFlatList } from '@gorhom/bottom-sheet';

import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import soraApi from '../services/soraApi';

// Helper to format time from seconds into HH:MM:SS or MM:SS
const formatTime = (s = 0) => {
    const seconds = Math.floor(Math.max(0, s));
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
};

const PlayerScreen = ({ route, navigation }) => {
  // --- Refs and Hooks ---
  const player = useRef(useVideoPlayer(null)).current;
  const { item } = route.params;
  const insets = useSafeAreaInsets();
  
  // --- Player State ---
  const [streamUrl, setStreamUrl] = useState(null);
  const [availableStreams, setAvailableStreams] = useState([]);
  const [selectedQuality, setSelectedQuality] = useState(null);
  const [playerState, setPlayerState] = useState({
    isPlaying: false,
    isBuffering: false,
    duration: 0,
    position: 0,
  });
  const { isPlaying, isBuffering, position, duration } = playerState;
  const [isLandscape, setIsLandscape] = useState(false);

  // --- UI Animated Values & Refs ---
  const controlsOpacity = useSharedValue(0);
  const controlsTimeout = useRef(null);
  const qualitySheetRef = useRef(null);

  // --- Effects and Callbacks ---
  
  // Load stream when the component is first opened or the item changes
  useEffect(() => { loadStreamData(); }, [item.id]);

  // Handle hiding the system UI (status bar) when in landscape mode
  useEffect(() => {
    StatusBar.setHidden(isLandscape, 'fade');
  }, [isLandscape]);
  
  // Manage screen orientation locking and unlocking when the screen is focused or blurred
  useFocusEffect(
    React.useCallback(() => {
      ScreenOrientation.unlockAsync();
      const sub = ScreenOrientation.addOrientationChangeListener(e => setIsLandscape(e.orientationInfo.orientation > 2));
      return () => { 
        if(player) player.pause();
        ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
        sub.remove();
      };
    }, [player])
  );
  
  // Polling for player state updates (this is a simple and stable approach)
  useEffect(() => {
    const interval = setInterval(() => {
      setPlayerState({
        isPlaying: player.isPlaying,
        isBuffering: player.isBuffering,
        duration: player.duration,
        position: player.currentTime
      });
    }, 250); // Poll 4 times per second
    return () => clearInterval(interval);
  }, [player]);

  // Manage auto-hiding of controls based on playback state
  useEffect(() => {
    isPlaying ? hideControls() : showControls(true);
  }, [isPlaying]);

  const loadStreamData = async () => {
    // This is a placeholder for your actual stream loading logic
    // Using try/catch to gracefully handle errors
    try {
        const data = await soraApi.getTVStreams(item.id, item.season, item.episode)
        const processed = soraApi.processStreamResponse(data)
        const bestStream = soraApi.getBestQualityStream(processed)

        if(bestStream?.url){
            setAvailableStreams(processed.streams);
            setSelectedQuality(bestStream);
            await player.replace(bestStream.url);
            await player.play();
            setStreamUrl(bestStream.url); // Set state to trigger render
        } else {
            throw new Error('No playable stream found');
        }
    } catch(e){
        Alert.alert('Playback Error', 'Unable to load video stream.', [{ text: 'OK', onPress: () => navigation.goBack() }]);
    }
  };
  
  // Function to change the video quality
  const changeQuality = async (newStream) => {
    if (!newStream || newStream.url === selectedQuality?.url) return;
    qualitySheetRef.current?.close();
    player.pause();
    const currentPos = position; // Store current time
    await player.replace(newStream.url);
    await player.seek(currentPos);
    player.play();
    setSelectedQuality(newStream);
  };
  
  // Functions to manage UI visibility
  const showControls = (permanent = false) => {
    clearTimeout(controlsTimeout.current);
    controlsOpacity.value = withTiming(1, { duration: 150 });
    if (!permanent && isPlaying) hideControls();
  };

  const hideControls = () => {
    clearTimeout(controlsTimeout.current);
    controlsTimeout.current = setTimeout(() => { controlsOpacity.value = withTiming(0); }, 3500);
  };
  
  // Player control functions
  const togglePlayPause = () => isPlaying ? player.pause() : player.play();
  const seekForward = () => player.currentTime += 10;
  const seekBackward = () => player.currentTime -= 10;
  
  // --- Gestures for player interaction ---
  const singleTap = Gesture.Tap().maxDuration(250).onStart(() => { controlsOpacity.value > 0 ? (controlsOpacity.value = withTiming(0)) : runOnJS(showControls)(); });
  const doubleTapLeft = Gesture.Tap().numberOfTaps(2).onStart(seekBackward);
  const doubleTapRight = Gesture.Tap().numberOfTaps(2).onStart(seekForward);
  
  const animatedControlsStyle = useAnimatedStyle(() => ({ opacity: controlsOpacity.value }));
  const bottomSheetPoints = useMemo(() => ['40%', '50%'], []);
  
  // Don't render anything until we at least attempt to get a stream URL
  if (!streamUrl && isLoading) return <View style={slothStyles.loadingContainer}><ActivityIndicator size="large" color="white"/></View>;

  return (
    <GestureHandlerRootView style={slothStyles.playerContainer}>
      <VideoView player={player} style={slothStyles.playerVideoView} contentFit="contain" />

      {isBuffering && <View style={slothStyles.playerLoadingContainer}><ActivityIndicator size="large" color="white" /></View>}
      
      {/* Invisible Gesture Overlay */}
      <GestureDetector gesture={singleTap}>
        <View style={{...StyleSheet.absoluteFillObject, flexDirection: 'row', zIndex: 1}}>
            <GestureDetector gesture={doubleTapLeft}><View style={{flex: 1}}/></GestureDetector>
            <GestureDetector gesture={doubleTapRight}><View style={{flex: 2}}/></GestureDetector>
        </View>
      </GestureDetector>

      {/* Animated Player UI */}
      <Animated.View style={[slothStyles.controlsOverlay, animatedControlsStyle]}>
        <LinearGradient colors={['rgba(0,0,0,0.5)', 'transparent']} style={{position:'absolute', top:0, left:0, right:0, height: insets.top + 60}}/>
        
        <View style={[slothStyles.topControls, { marginTop: insets.top, paddingHorizontal: isLandscape ? insets.left : 10 }]}>
            <TouchableOpacity style={slothStyles.playerIconButton} onPress={() => navigation.goBack()}><Ionicons name="arrow-back" size={26} color="white"/></TouchableOpacity>
            <Text style={slothStyles.playerTitle} numberOfLines={1}>{item.title || item.name}</Text>
            <TouchableOpacity style={slothStyles.playerIconButton} onPress={() => qualitySheetRef.current?.snapToIndex(0)}><Ionicons name="settings-outline" size={24} color="white"/></TouchableOpacity>
        </View>

        <View style={slothStyles.centerControls}>
            <TouchableOpacity onPress={togglePlayPause} style={slothStyles.playerIconButton}>
                <Ionicons name={isPlaying ? 'pause' : 'play'} size={50} color="white" />
            </TouchableOpacity>
        </View>

        <View style={{paddingBottom: isLandscape ? 5 : insets.bottom}}>
            <LinearGradient colors={['transparent', 'rgba(0,0,0,0.5)']} style={{position:'absolute', bottom:0, left:0, right:0, height: insets.bottom + 100}}/>
            <View style={[slothStyles.timecodeContainer, {paddingHorizontal: isLandscape ? insets.left + 5: 5}]}>
                <Text style={slothStyles.timecodeText}>{formatTime(position)}</Text>
                {/* Progress Bar */}
                <View style={{height: 3, flex: 1, backgroundColor: 'rgba(255,255,255,0.3)', marginHorizontal: 10}}>
                  <View style={{height: '100%', backgroundColor: SLOTH_COLORS.primary, width: `${(position/duration) * 100 || 0}%`}} />
                </View>
                <Text style={slothStyles.timecodeText}>{formatTime(duration)}</Text>
            </View>
        </View>
      </Animated.View>

      {/* Quality Selection Modal */}
      <BottomSheet
          ref={qualitySheetRef}
          index={-1} // Hidden by default
          snapPoints={bottomSheetPoints}
          enablePanDownToClose={true}
          handleIndicatorStyle={slothStyles.bottomSheetHandle}
          backgroundStyle={slothStyles.bottomSheetBackground}
        >
        <View>
          <Text style={slothStyles.bottomSheetHeaderText}>Select Quality</Text>
          <BottomSheetFlatList
            data={availableStreams}
            keyExtractor={s => s.name}
            renderItem={({ item: stream }) => (
              <TouchableOpacity style={slothStyles.bottomSheetItem} onPress={() => changeQuality(stream)}>
                <Text style={[slothStyles.bottomSheetItemText, selectedQuality?.name === stream.name && slothStyles.bottomSheetItemSelected]}>{stream.name}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </BottomSheet>
    </GestureHandlerRootView>
  );
};

export default PlayerScreen;